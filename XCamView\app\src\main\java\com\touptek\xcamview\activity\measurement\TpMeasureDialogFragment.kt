//package com.android.rockchip.camera2.activity.measurement
//
//import android.os.Bundle
//import android.view.*
//import androidx.fragment.app.DialogFragment
//import com.android.rockchip.camera2.R
//import android.widget.ImageButton
//import android.graphics.drawable.Drawable
//
//
//class TpMeasureDialogFragment : DialogFragment() {
//    private val buttons = mutableListOf<ImageButton>()
//
//    interface OnButtonClickListener {
//        fun onCalibrationClick(isSelected: Boolean)
//    }
//    private var buttonClickListener: OnButtonClickListener? = null
//    fun setOnButtonClickListener(listener: OnButtonClickListener) {
//        this.buttonClickListener = listener
//    }
//
//    override fun onCreateView(
//        inflater: LayoutInflater,
//        container: ViewGroup?,
//        savedInstanceState: Bundle? // 关键修正点：Bundle 改为 Bundle?
//    ): View? {
//        val view = inflater.inflate(R.layout.measurement_layout, container, false)
//        setupButtons(view)
//        return view
//    }
//
//    override fun onStart() {
//        super.onStart()
//        dialog?.window?.apply {
//            setDimAmount(0f)
////            setBackgroundDrawableResource(R.color.Light_black)
//            setBackgroundDrawableResource(R.color.white_background)
//            val params = attributes.apply {
//                width = WindowManager.LayoutParams.WRAP_CONTENT
//                height = WindowManager.LayoutParams.WRAP_CONTENT
//            }
//
//            arguments?.let { args ->
//                val marginPx = 20
//                val anchorX = args.getInt("anchor_x")
//                val anchorWidth = args.getInt("anchor_width")
//
//                // 横坐标保持不变的计算方式
//                val targetX = anchorX + anchorWidth + marginPx
//
//                // 纵坐标置顶（距离屏幕顶部20px）
//                val targetY = 0  // 可根据需要调整这个边距值
//
//                params.gravity = Gravity.TOP or Gravity.START
//                params.x = targetX
//                params.y = targetY
//            }
//
//            attributes = params
//        }
//    }
//
//
//    private fun setupButtons(view: View) {
//        val root = view as ViewGroup
//        for (i in 0 until root.childCount) {
//            val child = root.getChildAt(i)
//            if (child is ImageButton) {
//                buttons.add(child)
//                child.tag = child.background
//                child.setOnClickListener { toggleButtonState(child) }
//            }
//        }
//    }
//
//    private fun toggleButtonState(selectedButton: ImageButton) {
//        buttons.forEach { button ->
//            if (button == selectedButton) {
//                val wasSelected = button.isSelected
//                if (wasSelected) {
//                    button.background = button.tag as Drawable
//                    button.isSelected = false
//                } else {
//                    button.setBackgroundResource(R.drawable.grey_background)
//                    button.isSelected = true
//                }
//
//                // 处理特定按钮点击
//                when (button.id) {
//                    R.id.btn_calibration -> {
//                        buttonClickListener?.onCalibrationClick(!wasSelected)
//                        // 也可以直接在这里处理
//                        handleCalibrationClick(!wasSelected)
//                    }
//                    // 其他按钮的case可以继续添加...
//                }
//            } else {
//                button.background = button.tag as Drawable
//                button.isSelected = false
//            }
//        }
//    }
//
//    private fun handleCalibrationClick(isSelected: Boolean) {
//        if (isSelected) {
//            println("Calibration开始校准流程...")
//            // 在这里添加校准初始化逻辑
//        } else {
//            println("Calibration取消校准状态")
//            // 在这里添加校准取消逻辑
//        }
//    }
//
//}