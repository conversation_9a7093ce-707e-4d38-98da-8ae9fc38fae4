package com.touptek.xcamview.activity.ispdialogfragment
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageButton
import android.widget.RadioGroup
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.activity.MainMenu
import com.touptek.xcamview.util.BaseDialogFragment
import com.touptek.video.TpIspParam
import kotlin.collections.forEach
import kotlin.let
import kotlin.ranges.coerceIn

class TpWBDialogFragment : BaseDialogFragment(){
    private val handler = Handler(Looper.getMainLooper())
    private val seekBarListeners = mutableMapOf<Int, SeekBar.OnSeekBarChangeListener>()
    private var radioGroup: RadioGroup? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.whitebalance_layout, container, false)

        initTpIspParam()
        initSeekbar(view)
        initRadioGroup(view)
        initButtonControls(view)
        initParameterValue(view)


        TpIspParam.addOnDataChangedListener(object : TpIspParam.OnDataChangedListener {
            override fun onDataChanged(param: TpIspParam, newValue: Int) {
                handler.post {
                    updateSeekBar(param, newValue) // 调用更新方法
                }
            }

            override fun onLongDataChanged(param: TpIspParam, newValue: Long) {
            }
        })

        return view
    }


    override fun onStart() {
        super.onStart()

//        dialog?.window?.apply {
//            addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL)
//        }

        val dialog = dialog
        if (dialog != null) {
            val window = dialog.window
            if (window != null) {
                window.setDimAmount(0f)
                window.setBackgroundDrawableResource(android.R.color.transparent)

                val params = window.attributes

                // 获取传递过来的位置和宽度参数
                val args = arguments
                if (args != null) {
                    val buttonX = args.getInt("x", 0)
                    val buttonY = args.getInt("y", 0)
                    val buttonWidth = args.getInt("width", 0)

                    // 设置对话框的位置，使其显示在按钮的右边
                    params.x = buttonX + buttonWidth + 25 // 右边偏移按钮宽度
                    params.y = buttonY - 315 // 同按钮的Y轴位置
                }

                params.width = WindowManager.LayoutParams.WRAP_CONTENT
                params.height = WindowManager.LayoutParams.WRAP_CONTENT
                params.gravity = Gravity.TOP or Gravity.START // 从左上角开始定位
                window.attributes = params
            }
        }
    }

    private fun initTpIspParam() {
//        TpIspParam.init(context)
//        // 设置串口状态变化监听器
//        TpIspParam.setOnSerialStateChangedListener { connected ->
//            if (connected){
//                showToast("串口已连接")
//            }else{
//                showToast("串口断开")
//            }
//        }
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun initParameterValue(view: View) {
        val radioGroup = view.findViewById<RadioGroup>(R.id.wb_btn_group)
        val savedSelection = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_WBCHOICE)

        when (savedSelection) {
            0 -> radioGroup.check(R.id.radio_manual_tv)
            1 -> radioGroup.check(R.id.radio_auto_tv)
            2 -> {
                radioGroup.check(R.id.radio_roi_tv)
                (parentFragmentManager.findFragmentByTag("MainMenu") as? MainMenu)?.displayRectangle()
            }
        }

        setSeekBarProgress(
            view,
            R.id.seekbar_red_tv,
            R.id.text_red_value,
            TpIspParam.TOUPTEK_PARAM_WBREDGAIN
        )
        setSeekBarProgress(
            view,
            R.id.seekbar_green_tv,
            R.id.text_green_value,
            TpIspParam.TOUPTEK_PARAM_WBGREENGAIN
        )
        setSeekBarProgress(
            view,
            R.id.seekbar_blue_tv,
            R.id.text_blue_value,
            TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN
        )
    }

    private fun initSeekbar(view: View) {
        setupSeekBar(
            view,
            R.id.seekbar_red_tv,
            R.id.text_red_value,
            TpIspParam.TOUPTEK_PARAM_WBREDGAIN
        )

        setupSeekBar(
            view,
            R.id.seekbar_green_tv,
            R.id.text_green_value,
            TpIspParam.TOUPTEK_PARAM_WBGREENGAIN
        )

        setupSeekBar(
            view,
            R.id.seekbar_blue_tv,
            R.id.text_blue_value,
            TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN
        )

    }

    private fun initRadioGroup(view: View) {
        radioGroup = view.findViewById<RadioGroup>(R.id.wb_btn_group)
        radioGroup!!.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio_auto_tv -> {
                    TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, 1)
                    disableSeekBar(view, R.id.seekbar_red_tv)
                    disableSeekBar(view, R.id.seekbar_green_tv)
                    disableSeekBar(view, R.id.seekbar_blue_tv)

                    changeAdjustBtnState(true)

                    (parentFragmentManager.findFragmentByTag("MainMenu") as? MainMenu)?.hideRectangle()
                }
                R.id.radio_manual_tv -> {
                    TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, 0)

                    enableSeekBar(view, R.id.seekbar_red_tv)
                    enableSeekBar(view, R.id.seekbar_green_tv)
                    enableSeekBar(view, R.id.seekbar_blue_tv)
                    changeAdjustBtnState(false)

                    (parentFragmentManager.findFragmentByTag("MainMenu") as? MainMenu)?.hideRectangle()
                }
                R.id.radio_roi_tv -> {
                    TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, 2)

                    disableSeekBar(view, R.id.seekbar_red_tv)
                    disableSeekBar(view, R.id.seekbar_green_tv)
                    disableSeekBar(view, R.id.seekbar_blue_tv)
                    changeAdjustBtnState(true)

                    (parentFragmentManager.findFragmentByTag("MainMenu") as? MainMenu)?.displayRectangle()
                }
            }
        }

    }

    private fun setupSeekBar(view: View, seekBarId: Int, textViewId: Int, param: TpIspParam) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        seekBar.min = TpIspParam.getMinValue(param)
        seekBar.max = TpIspParam.getMaxValue(param)
        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                TpIspParam.updateParam(TpIspParam.valueOf(param.name),progress)
                updateSeekBar(param, progress)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
                // 可在此处添加触摸开始时的逻辑
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                // 可在此处添加触摸结束时的逻辑
            }
        })
    }

    private fun disableSeekBar(view: View, seekBarId: Int) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        seekBar.isEnabled = false
    }

    private fun enableSeekBar(view: View, seekBarId: Int) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        seekBar.isEnabled = true
    }

    private fun setSeekBarProgress(
        view: View,
        seekBarId: Int,
        textViewId: Int,
        param: TpIspParam
    ) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        val textView = view.findViewById<TextView>(textViewId)
        val value = TpIspParam.getCurrentValue(param) // 获取保存的进度值
        Log.d("setSeekBarProgress 111","value:${value},ViewId:${param}")
        seekBar.progress = value // 设置 SeekBar 的进度
        textView.text = value.toString() // 设置 TextView 显示当前值
    }

    private fun updateSeekBar(param: TpIspParam, newValue: Int) {
        if (view == null) return  // 避免空指针

        var seekBarId = -1
        var textViewId = -1

        when (param) {
            TpIspParam.TOUPTEK_PARAM_WBREDGAIN -> {
                seekBarId = R.id.seekbar_red_tv
                textViewId = R.id.text_red_value
            }

            TpIspParam.TOUPTEK_PARAM_WBGREENGAIN -> {
                seekBarId = R.id.seekbar_green_tv
                textViewId = R.id.text_green_value
            }

            TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN -> {
                seekBarId = R.id.seekbar_blue_tv
                textViewId = R.id.text_blue_value
            }
            else -> return
        }

        val rootView = requireView()
        val seekBar = rootView.findViewById<SeekBar>(seekBarId)
        val textView = rootView.findViewById<TextView>(textViewId)

        if (seekBar != null && textView != null) {
            seekBar.progress = newValue
            textView.text = newValue.toString()
        }
    }

    private fun handleWBDefaultButtonClick(view: View) {
        // 在这里添加捕获按钮点击时的操作，例如开始捕获图像
        Toast.makeText(activity, "恢复白平衡默认状态", Toast.LENGTH_SHORT).show()
        setDefaultParameter(view)
    }

    private fun initButtonControls(view: View) {
        // 配置按钮的短按和长按行为
        setupButtonControl(view, R.id.btn_red_reduce, R.id.seekbar_red_tv, -1, -2)
        setupButtonControl(view, R.id.btn_red_add, R.id.seekbar_red_tv, +1, +2)
        setupButtonControl(view, R.id.btn_green_reduce, R.id.seekbar_green_tv, -1, -1)
        setupButtonControl(view, R.id.btn_green_add, R.id.seekbar_green_tv, +1, +1)
        setupButtonControl(view, R.id.btn_blue_reduce, R.id.seekbar_blue_tv, -1, -2)
        setupButtonControl(view, R.id.btn_blue_add, R.id.seekbar_blue_tv, +1, +2)

        val buttonWBDefault = view.findViewById<Button>(R.id.btn_Default_wb)
        buttonWBDefault.setOnClickListener { v: View? -> handleWBDefaultButtonClick(view) }
    }

    private fun setupButtonControl(
        view: View,
        buttonId: Int,
        seekBarId: Int,
        shortIncrement: Int,
        longIncrement: Int
    ) {
        val button = view.findViewById<ImageButton>(buttonId)
        // 设置短按点击监听
        button.setOnClickListener {
            setShortPress(seekBarId, shortIncrement)
        }
        // 设置长按持续操作
        setupLongPress(button, seekBarId, longIncrement)
    }

    private fun setShortPress(seekBarId: Int, delta: Int) {
        val seekBar = view?.findViewById<SeekBar>(seekBarId) ?: return
        val newValue = (seekBar.progress + delta).coerceIn(seekBar.min, seekBar.max)

        if (newValue != seekBar.progress) {
            seekBar.progress = newValue
            seekBarListeners[seekBarId]?.onProgressChanged(seekBar, newValue, false)
        }
    }

    private fun setupLongPress(button: ImageButton, seekBarId: Int, delta: Int) {
        var delayRunnable: Runnable? = null
        var periodicRunnable: Runnable? = null
        var isLongPressTriggered = false // 标志是否已触发长按

        button.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    isLongPressTriggered = false // 重置标志
                    // 移除之前的回调
                    delayRunnable?.let { handler.removeCallbacks(it) }
                    periodicRunnable?.let { handler.removeCallbacks(it) }

                    // 设置延迟任务：500ms后触发
                    delayRunnable = Runnable {
                        isLongPressTriggered = true // 标记长按已触发
                        periodicRunnable = object : Runnable {
                            override fun run() {
                                setShortPress(seekBarId, delta)
                                handler.postDelayed(this, 100)
                            }
                        }
                        periodicRunnable?.let { handler.post(it) }
                    }
                    handler.postDelayed(delayRunnable!!, 300)
                    false // 不消费事件，允许触发单击
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 移除所有回调
                    delayRunnable?.let { handler.removeCallbacks(it) }
                    periodicRunnable?.let { handler.removeCallbacks(it) }
                    // 根据是否触发长按决定是否消费事件
                    if (isLongPressTriggered) {
                        isLongPressTriggered = false
                        true // 消费事件，阻止单击触发
                    } else {
                        false // 允许单击事件
                    }
                }
                else -> false
            }
        }
    }

    private fun changeAdjustBtnState(enable: Boolean) {
        // 所有调节按钮的ID数组（包含需要反转的按钮）
        val adjustButtonIds = arrayOf(
            R.id.btn_red_reduce,
            R.id.btn_red_add,
            R.id.btn_green_reduce,
            R.id.btn_green_add,
            R.id.btn_blue_reduce,
            R.id.btn_blue_add
        )

        view?.let { rootView ->
            adjustButtonIds.forEach { buttonId ->
                // 根据按钮ID决定是否反转状态
                rootView.findViewById<ImageButton>(buttonId)?.isEnabled = !enable
            }
        }

    }

    private fun setDefaultParameter(view: View) {
        val radioGroup = view.findViewById<RadioGroup>(R.id.wb_btn_group)
        radioGroup.check(R.id.radio_auto_tv)
        changeAdjustBtnState(true)
    }

}