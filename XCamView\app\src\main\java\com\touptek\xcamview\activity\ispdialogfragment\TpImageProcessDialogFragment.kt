package com.touptek.xcamview.activity.ispdialogfragment
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageButton
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.video.TpIspParam
import kotlin.let
import kotlin.ranges.coerceIn

class TpImageProcessDialogFragment : DialogFragment(){
    private val handler = Handler(Looper.getMainLooper())
    private val seekBarListeners = mutableMapOf<Int, SeekBar.OnSeekBarChangeListener>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.image_parameter_layout, container, false)
        initTpIspParam()
        initParameterValue(view)
        initSeekbar(view)
        initButtonControls(view)
        return view
    }


    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            val window = dialog.window
            if (window != null) {
                window.setDimAmount(0f)
                window.setBackgroundDrawableResource(android.R.color.transparent)
                val params = window.attributes

                // 获取传递过来的位置和宽度参数
                val args = arguments
                if (args != null) {
                    val buttonX = args.getInt("x", 0)
                    val buttonY = args.getInt("y", 0)
                    val buttonWidth = args.getInt("width", 0)

                    // 设置对话框的位置，使其显示在按钮的右边
                    params.x = buttonX + buttonWidth + 25 // 右边偏移按钮宽度
                    params.y = buttonY - 455 // 同按钮的Y轴位置
                }

                params.width = WindowManager.LayoutParams.WRAP_CONTENT
                params.height = WindowManager.LayoutParams.WRAP_CONTENT
                params.gravity = Gravity.TOP or Gravity.START // 从左上角开始定位
                window.attributes = params
            }
        }
    }

    private fun initTpIspParam() {
//        TpIspParam.init(context)
//        // 设置串口状态变化监听器
//        TpIspParam.setOnSerialStateChangedListener { connected ->
//            if (connected){
//                showToast("串口已连接")
//            }else{
//                showToast("串口断开")
//            }
//        }
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun initParameterValue(view: View) {
        setSeekBarProgress(
            view,
            R.id.seekbar_saturation_tv,
            R.id.text_saturation_value,
            TpIspParam.TOUPTEK_PARAM_SATURATION
        )
        setSeekBarProgress(
            view,
            R.id.seekbar_gamma_tv,
            R.id.text_gamma_value,
            TpIspParam.TOUPTEK_PARAM_GAMMA
        )
        setSeekBarProgress(
            view,
            R.id.seekbar_contrast_tv,
            R.id.text_contrast_value,
            TpIspParam.TOUPTEK_PARAM_CONTRAST
        )
        setSeekBarProgress(
            view,
            R.id.seekbar_brightness_tv,
            R.id.text_brightness_value,
            TpIspParam.TOUPTEK_PARAM_BRIGHTNESS
        )
        setSeekBarProgress(
            view,
            R.id.seekbar_sharpness_tv,
            R.id.text_sharpness_value,
            TpIspParam.TOUPTEK_PARAM_SHARPNESS
        )
        setSeekBarProgress(
            view,
            R.id.seekbar_denoise_tv,
            R.id.text_denoise_value,
            TpIspParam.TOUPTEK_PARAM_DENOISE
        )
    }

    private fun setSeekBarProgress(
        view: View,
        seekBarId: Int,
        textViewId: Int,
        param: TpIspParam
    ) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        val textView = view.findViewById<TextView>(textViewId)
        val value = TpIspParam.getCurrentValue(param) // 获取保存的进度值

        seekBar.progress = value // 设置 SeekBar 的进度
        textView.text = value.toString() // 设置 TextView 显示当前值
    }

    private fun initSeekbar(view: View) {
        setupSeekBar(
            view,
            R.id.seekbar_saturation_tv,
            R.id.text_saturation_value,
            TpIspParam.TOUPTEK_PARAM_SATURATION
        )

        setupSeekBar(
            view,
            R.id.seekbar_gamma_tv,
            R.id.text_gamma_value,
            TpIspParam.TOUPTEK_PARAM_GAMMA
        )

        setupSeekBar(
            view,
            R.id.seekbar_contrast_tv,
            R.id.text_contrast_value,
            TpIspParam.TOUPTEK_PARAM_CONTRAST
        )

        setupSeekBar(
            view,
            R.id.seekbar_brightness_tv,
            R.id.text_brightness_value,
            TpIspParam.TOUPTEK_PARAM_BRIGHTNESS
        )

        setupSeekBar(
            view,
            R.id.seekbar_sharpness_tv,
            R.id.text_sharpness_value,
            TpIspParam.TOUPTEK_PARAM_SHARPNESS
        )

        setupSeekBar(
            view,
            R.id.seekbar_denoise_tv,
            R.id.text_denoise_value,
            TpIspParam.TOUPTEK_PARAM_DENOISE
        )

    }

    private fun setupSeekBar(view: View, seekBarId: Int, textViewId: Int, param: TpIspParam) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        seekBar.min = TpIspParam.getMinValue(param)
        seekBar.max = TpIspParam.getMaxValue(param)
        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                TpIspParam.updateParam(TpIspParam.valueOf(param.name),progress)
                updateSeekBar(param, progress)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
                // 可在此处添加触摸开始时的逻辑
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                // 可在此处添加触摸结束时的逻辑
            }
        })
    }

    private fun updateSeekBar(param: TpIspParam, newValue: Int) {
        if (view == null) return  // 避免空指针
        var seekBarId = -1
        var textViewId = -1

        when (param) {
            TpIspParam.TOUPTEK_PARAM_SATURATION -> {
                seekBarId = R.id.seekbar_saturation_tv
                textViewId = R.id.text_saturation_value
            }

            TpIspParam.TOUPTEK_PARAM_GAMMA-> {
                seekBarId = R.id.seekbar_gamma_tv
                textViewId = R.id.text_gamma_value
            }

            TpIspParam.TOUPTEK_PARAM_CONTRAST -> {
                seekBarId = R.id.seekbar_contrast_tv
                textViewId = R.id.text_contrast_value
            }

            TpIspParam.TOUPTEK_PARAM_BRIGHTNESS -> {
                seekBarId = R.id.seekbar_brightness_tv
                textViewId = R.id.text_brightness_value
            }

            TpIspParam.TOUPTEK_PARAM_SHARPNESS -> {
                seekBarId = R.id.seekbar_sharpness_tv
                textViewId = R.id.text_sharpness_value
            }

            TpIspParam.TOUPTEK_PARAM_DENOISE-> {
                seekBarId = R.id.seekbar_denoise_tv
                textViewId = R.id.text_denoise_value
            }

            else -> return
        }

        val rootView = requireView()
        val seekBar = rootView.findViewById<SeekBar>(seekBarId)
        val textView = rootView.findViewById<TextView>(textViewId)

        if (seekBar != null && textView != null) {
            seekBar.progress = newValue
            textView.text = newValue.toString()
        }
    }

    private fun handleImageDefaultButtonClick(view: View) {
        // 在这里添加捕获按钮点击时的操作，例如开始捕获图像
        Toast.makeText(activity, "恢复默认四", Toast.LENGTH_SHORT).show()
        setDefaultParameter(view)
    }

    private fun setDefaultParameter(view: View) {
//        changeAdjustBtnState(false)
        TpIspParam.requestAllParamRanges()

        val saturationValue = TpIspParam.getDefaultValue(TpIspParam.TOUPTEK_PARAM_SATURATION)
        TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_SATURATION,saturationValue)
        updateSeekBar(TpIspParam.TOUPTEK_PARAM_SATURATION,saturationValue)

        val gammaValue = TpIspParam.getDefaultValue(TpIspParam.TOUPTEK_PARAM_GAMMA)
        TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_GAMMA,gammaValue)
        updateSeekBar(TpIspParam.TOUPTEK_PARAM_GAMMA,gammaValue)

        val contrastValue = TpIspParam.getDefaultValue(TpIspParam.TOUPTEK_PARAM_CONTRAST)
        TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_CONTRAST,contrastValue)
        updateSeekBar(TpIspParam.TOUPTEK_PARAM_CONTRAST,contrastValue)

        val brightnessValue = TpIspParam.getDefaultValue(TpIspParam.TOUPTEK_PARAM_BRIGHTNESS)
        TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_BRIGHTNESS,brightnessValue)
        updateSeekBar(TpIspParam.TOUPTEK_PARAM_BRIGHTNESS,brightnessValue)

        val sharpnessValue = TpIspParam.getDefaultValue(TpIspParam.TOUPTEK_PARAM_SHARPNESS)
        TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_SHARPNESS,sharpnessValue)
        updateSeekBar(TpIspParam.TOUPTEK_PARAM_SHARPNESS,sharpnessValue)

        val DenoiseValue = TpIspParam.getDefaultValue(TpIspParam.TOUPTEK_PARAM_DENOISE)
        TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_DENOISE,DenoiseValue)
        updateSeekBar(TpIspParam.TOUPTEK_PARAM_DENOISE,DenoiseValue)
    }

    private fun changeAdjustBtnState(enable: Boolean) {
//        val adjustButtonIds = arrayOf(
//            R.id.btn_saturation_reduce,
//            R.id.btn_saturation_add,
//            R.id.btn_gamma_reduce,
//            R.id.btn_gamma_add,
//            R.id.btn_contrast_reduce,
//            R.id.btn_contrast_add,
//            R.id.btn_brightness_reduce,
//            R.id.btn_brightness_add
//        )
//
//        view?.let { rootView ->
//            adjustButtonIds.forEach { buttonId ->
//                // 根据按钮ID决定是否反转状态
//                rootView.findViewById<ImageButton>(buttonId)?.isEnabled = !enable
//            }
//        }
    }

    private fun initButtonControls(view: View) {
        // 配置按钮的短按和长按行为
        setupButtonControl(view, R.id.btn_saturation_reduce, R.id.seekbar_saturation_tv, -1, -2)
        setupButtonControl(view, R.id.btn_saturation_add, R.id.seekbar_saturation_tv, +1, +2)
        setupButtonControl(view, R.id.btn_gamma_reduce, R.id.seekbar_gamma_tv, -1, -1)
        setupButtonControl(view, R.id.btn_gamma_add, R.id.seekbar_gamma_tv, +1, +1)
        setupButtonControl(view, R.id.btn_contrast_reduce, R.id.seekbar_contrast_tv, -1, -2)
        setupButtonControl(view, R.id.btn_contrast_add, R.id.seekbar_contrast_tv, +1, +2)
        setupButtonControl(view, R.id.btn_brightness_reduce, R.id.seekbar_brightness_tv, -1, -2)
        setupButtonControl(view, R.id.btn_brightness_add, R.id.seekbar_brightness_tv, +1, +2)
        setupButtonControl(view, R.id.btn_sharpness_reduce, R.id.seekbar_sharpness_tv, -1, -5)
        setupButtonControl(view, R.id.btn_sharpness_add, R.id.seekbar_sharpness_tv, +1, +5)
        setupButtonControl(view, R.id.btn_denoise_reduce, R.id.seekbar_denoise_tv, -1, -3)
        setupButtonControl(view, R.id.btn_denoise_add, R.id.seekbar_denoise_tv, +1, +3)

        val buttonImageDefault = view.findViewById<Button>(R.id.btn_Default_image_parameter)
        buttonImageDefault.setOnClickListener { v: View? -> handleImageDefaultButtonClick(view) }
    }

    private fun setupButtonControl(
        view: View,
        buttonId: Int,
        seekBarId: Int,
        shortIncrement: Int,
        longIncrement: Int
    ) {
        val button = view.findViewById<ImageButton>(buttonId)
        // 设置短按点击监听
        button.setOnClickListener {
            setShortPress(seekBarId, shortIncrement)
        }
        // 设置长按持续操作
        setupLongPress(button, seekBarId, longIncrement)
    }

    private fun setShortPress(seekBarId: Int, delta: Int) {
        val seekBar = view?.findViewById<SeekBar>(seekBarId) ?: return
        val newValue = (seekBar.progress + delta).coerceIn(seekBar.min, seekBar.max)

        if (newValue != seekBar.progress) {
            seekBar.progress = newValue
            seekBarListeners[seekBarId]?.onProgressChanged(seekBar, newValue, false)
        }
    }

    private fun setupLongPress(button: ImageButton, seekBarId: Int, delta: Int) {
        var delayRunnable: Runnable? = null
        var periodicRunnable: Runnable? = null
        var isLongPressTriggered = false // 标志是否已触发长按

        button.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    isLongPressTriggered = false // 重置标志
                    // 移除之前的回调
                    delayRunnable?.let { handler.removeCallbacks(it) }
                    periodicRunnable?.let { handler.removeCallbacks(it) }

                    // 设置延迟任务：500ms后触发
                    delayRunnable = Runnable {
                        isLongPressTriggered = true // 标记长按已触发
                        periodicRunnable = object : Runnable {
                            override fun run() {
                                setShortPress(seekBarId, delta)
                                handler.postDelayed(this, 100)
                            }
                        }
                        periodicRunnable?.let { handler.post(it) }
                    }
                    handler.postDelayed(delayRunnable!!, 300)
                    false // 不消费事件，允许触发单击
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 移除所有回调
                    delayRunnable?.let { handler.removeCallbacks(it) }
                    periodicRunnable?.let { handler.removeCallbacks(it) }
                    // 根据是否触发长按决定是否消费事件
                    if (isLongPressTriggered) {
                        isLongPressTriggered = false
                        true // 消费事件，阻止单击触发
                    } else {
                        false // 允许单击事件
                    }
                }
                else -> false
            }
        }
    }
}