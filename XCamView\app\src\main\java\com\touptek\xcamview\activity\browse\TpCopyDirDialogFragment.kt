package com.touptek.xcamview.activity.browse

import android.app.ProgressDialog
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.touptek.xcamview.R
import com.touptek.xcamview.util.dpToPx
import java.io.File
import java.io.IOException
import java.util.ArrayList
import kotlin.also
import kotlin.apply
import kotlin.collections.forEach
import kotlin.collections.isNotEmpty
import kotlin.collections.map
import kotlin.collections.sortedBy
import kotlin.collections.sumOf
import kotlin.collections.take
import kotlin.collections.toList
import kotlin.collections.withIndex
import kotlin.io.inputStream
import kotlin.io.outputStream
import kotlin.io.use
import kotlin.let

class TpCopyDirDialogFragment : DialogFragment() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: TpOperationDirAdapter
    private lateinit var currentDir: File
    private var rootDir: File? = null
    private var filesToCopy: List<String> = emptyList()
    private var copyThread: Thread? = null
    private var progressDialog: ProgressDialog? = null

    interface OnMoveCompleteListener {
        fun onMoveComplete(success: Boolean)
    }
    private var moveCompleteListener: OnMoveCompleteListener? = null

    companion object {
        private const val KEY_ROOT_DIR = "root_dir"
        private const val KEY_CURRENT_DIR = "current_dir"
        private const val KEY_COPY_FILES = "copy_files"
        const val OPERATION_COPY = "copy"
        const val OPERATION_CUT = "cut"
        const val OPERATION_DELETE = "delete"
        private const val KEY_OPERATION_TYPE = "operation_type"
        fun newInstance(
            rootDir: File,
            currentDir: File,
            filePaths: List<String>,
            operationType: String // 添加操作类型参数
        ): TpCopyDirDialogFragment {
            val fragment = TpCopyDirDialogFragment()
            val args = Bundle().apply {
                putString(KEY_ROOT_DIR, rootDir.absolutePath)
                putString(KEY_CURRENT_DIR, currentDir.absolutePath)
                putStringArrayList(KEY_COPY_FILES, ArrayList(filePaths))
                putString(KEY_OPERATION_TYPE, operationType) // 保存操作类型
            }
            fragment.arguments = args
            return fragment
        }
        private var operationType: String = OPERATION_COPY
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.copydialog_settings, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            rootDir = File(it.getString(KEY_ROOT_DIR)!!)
            currentDir = File(it.getString(KEY_CURRENT_DIR)!!)
            filesToCopy = it.getStringArrayList(KEY_COPY_FILES) ?: emptyList()
            operationType = it.getString(KEY_OPERATION_TYPE) ?: OPERATION_COPY
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 初始化目录
        arguments?.let {
            rootDir = File(it.getString(KEY_ROOT_DIR)!!)
            currentDir = File(it.getString(KEY_CURRENT_DIR)!!)
        }

        // 设置标题
        view.findViewById<TextView>(R.id.tv_title).text = ""

        recyclerView = view.findViewById(R.id.rv_folders)
        setupAdapter()
        loadFolders()

        // 关闭按钮
        view.findViewById<ImageButton>(R.id.btn_close).setOnClickListener {
            dismiss()
        }

        // 确认按钮
        view.findViewById<View>(R.id.btn_confirm).setOnClickListener {
            copyFilesTo(currentDir)
        }
    }

    private fun setupAdapter() {
        try {
            // 检查关键对象是否初始化
            if (!::recyclerView.isInitialized || context == null) {
                return
            }

            // 获取屏幕宽度计算列数
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val columnWidth = try {
                resources.getDimensionPixelSize(R.dimen.folder_item_width)
            } catch (e: Exception) {
                120.dpToPx() // 默认120dp
            }
            val columns = maxOf(2, screenWidth / columnWidth)

            // 设置布局管理器
            recyclerView.layoutManager = GridLayoutManager(context, columns)

            // 初始化适配器
            adapter = TpOperationDirAdapter(
                imageFiles = emptyList(),
                labels = emptyList(),
                onClick = { position ->
                    try {
                        val clickedFile = adapter.getFileAt(position)
                        currentDir = if (clickedFile.name == "..") {
                            clickedFile.parentFile ?: rootDir ?: clickedFile
                        } else {
                            clickedFile
                        }
                        loadFolders()

                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                },
                onDoubleClick = { position -> },
                onUpdate = { /* 可选更新操作 */ }
            ).apply {
                allowSelectionMode = false
            }

            recyclerView.adapter = adapter
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun loadFolders() {
        // 获取所有子文件夹
        val folders = currentDir.listFiles { file ->
            file.isDirectory && !file.isHidden
        }?.toList() ?: emptyList()

        // 如果有父目录且不是根目录，添加上级目录 ".."
        val parentDir = currentDir.parentFile
        val items = mutableListOf<File>().apply {
            if (parentDir != null && parentDir != rootDir) {
                add(File(currentDir.parent))
            }
            addAll(folders.sortedBy { it.name })
        }

        // 生成文件夹名称列表 (显示简短的名称)
        val labels = items.map { file ->
            if (file.name == currentDir.parent) ".." else file.name
        }

        adapter.updateData(items, labels)

        // 更新当前路径显示
        view?.findViewById<TextView>(R.id.tv_current_path)?.text = currentDir.absolutePath

        // 更新标题显示当前操作类型
        view?.findViewById<TextView>(R.id.tv_title)?.text = when (operationType) {
            OPERATION_COPY -> "选择目标文件夹(复制)"
            OPERATION_CUT -> "选择目标文件夹(移动)"
            OPERATION_DELETE -> "确认删除位置"
            else -> "选择目标文件夹"
        }

        // 更新返回按钮可见性
//        updateBackButtonVisibility()
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            val params = attributes.apply {
                val displayMetrics = resources.displayMetrics
                val screenWidth = displayMetrics.widthPixels
                val screenHeight = displayMetrics.heightPixels
                setLayout((screenWidth * 0.7).toInt(), (screenHeight * 0.7).toInt())
                setGravity(Gravity.CENTER)
            }
            // 添加边框和阴影效果
            setBackgroundDrawable(resources.getDrawable(R.drawable.dialog_border, null))
            attributes = params
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun copyFilesTo(targetDir: File) {
        if (filesToCopy.isEmpty()) {
            showToast("没有选中文件")
            return
        }

        // 如果是删除操作，显示确认对话框
        if (operationType == OPERATION_DELETE) {
            showDeleteConfirmationDialog(targetDir)
            return
        }

        // 创建并显示进度对话框
        val progressDialog = createProgressDialog(filesToCopy.size) ?: run {
            showToast("无法创建进度对话框")
            return
        }

        progressDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "取消") { dialog, _ ->
            dialog.dismiss()
            copyThread?.interrupt()
        }
        progressDialog.show()

        // 使用工作线程执行复制
        copyThread = Thread {
            var successCount = 0
            var failedCount = 0
            val errors = mutableListOf<String>()

            // 统计总文件大小（用于进度计算）
            val totalSize = filesToCopy.sumOf { File(it).length() }
            var copiedSize: Long = 0

            for ((index, sourcePath) in filesToCopy.withIndex()) {
                try {
                    val srcFile = File(sourcePath)

                    // 更新当前文件名显示
                    activity?.runOnUiThread {
                        if (progressDialog.isShowing) {
                            progressDialog.setMessage("复制: ${srcFile.name}")
                        }
                    }

                    val destFile = File(targetDir, srcFile.name)

                    // 使用缓冲流复制以便显示进度
                    srcFile.inputStream().use { input ->
                        destFile.outputStream().use { output ->
                            val buffer = ByteArray(1024 * 1024) // 1MB 缓冲区
                            var bytesRead: Int
                            var totalFileCopied: Long = 0
                            val fileSize = srcFile.length()

                            while (input.read(buffer).also { bytesRead = it } >= 0) {
                                output.write(buffer, 0, bytesRead)
                                totalFileCopied += bytesRead
                                copiedSize += bytesRead

                                // 每复制1%更新一次进度，避免过于频繁
                                if (totalFileCopied % (fileSize / 100) == 0L || totalFileCopied == fileSize) {
                                    val percent = ((copiedSize * 100) / totalSize).toInt()
                                    activity?.runOnUiThread {
                                        progressDialog.progress = percent
                                    }
                                }
                            }
                        }
                    }

                    //若为剪切，复制成功后删除原文件
                    if (operationType == OPERATION_CUT) {
                        val srcFile = File(sourcePath)
                        if (!srcFile.delete()) {
                            throw IOException("Failed to delete original file")
                        }
                    }

                    successCount++
                } catch (e: Exception) {
                    e.printStackTrace()
                    errors.add("复制失败: ${e.message} [${File(sourcePath).name}]")
                    failedCount++
                }

                // 更新进度条步数
                val currentProgress = ((index + 1) * 100) / filesToCopy.size
                activity?.runOnUiThread {
                    progressDialog.progress = currentProgress
                }
            }

            // 更新UI（在主线程执行）
            activity?.runOnUiThread {
                try {
                    if (progressDialog.isShowing) {
                        progressDialog.dismiss()
                    }
                    dismiss() // 关闭文件夹选择对话框
                } catch (e: Exception) {
                    e.printStackTrace()
                }

                val operationName = if (operationType == OPERATION_CUT) "移动" else "复制"
                val msg = buildString {
                    append("${operationName}完成\n")
                    append("成功: $successCount, 失败: $failedCount\n")
                    if (errors.isNotEmpty()) {
                        append("\n错误列表:")
                        errors.take(5).forEach { append("\n- $it") }
                        if (errors.size > 5) append("\n...还有 ${errors.size - 5} 个错误未显示")
                    }
                }

                // 传递操作完成状态（复制/移动/删除）
                println("#####@ 操作完成回调: type=$operationType, success=$successCount")
                moveCompleteListener?.onMoveComplete(successCount > 0)

                AlertDialog.Builder(requireContext())
                    .setTitle("复制结果")
                    .setMessage(msg)
                    .setPositiveButton("确定") { _, _ ->
                        // 可选：完成复制后的操作
                    }
                    .show()
            }
        }.apply {
            start()
        }
    }

    private fun createProgressDialog(max: Int): ProgressDialog {
        return ProgressDialog(requireContext()).apply {
            setTitle(
                when (operationType) {
                    OPERATION_COPY -> "正在复制文件"
                    OPERATION_CUT -> "正在移动文件"
                    OPERATION_DELETE -> "正在删除文件"
                    else -> "正在操作文件"
                }
            )
            setMessage("准备操作...")
            setProgressStyle(ProgressDialog.STYLE_HORIZONTAL)
            setCancelable(false)
            setMax(100)
        }
    }

    private fun showDeleteConfirmationDialog(targetDir: File) {
        AlertDialog.Builder(requireContext())
            .setTitle("确认删除")
            .setMessage("确定要删除选中的${filesToCopy.size}个文件吗？")
            .setPositiveButton("删除") { _, _ ->
                performDeleteOperation(targetDir)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun performDeleteOperation(targetDir: File) {
        // 创建并显示进度对话框
        val progressDialog = createProgressDialog(filesToCopy.size).apply {
            // 添加取消按钮
            setButton(DialogInterface.BUTTON_NEGATIVE, "取消") { dialog, _ ->
                dialog.dismiss()
                copyThread?.interrupt()
            }
            show()
        }

        // 使用工作线程执行删除
        copyThread = Thread {
            var successCount = 0
            var failedCount = 0
            val errors = mutableListOf<String>()

            for ((index, sourcePath) in filesToCopy.withIndex()) {
                try {
                    val srcFile = File(sourcePath)

                    // 更新当前文件名显示
                    activity?.runOnUiThread {
                        progressDialog.setMessage("删除: ${srcFile.name}")
                    }

                    if (!srcFile.delete()) {
                        throw IOException("删除文件失败")
                    }

                    successCount++

                } catch (e: Exception) {
                    e.printStackTrace()
                    errors.add("删除失败: ${e.message} [${File(sourcePath).name}]")
                    failedCount++
                }
                // 更新进度条步数
                val currentProgress = ((index + 1) * 100) / filesToCopy.size
                activity?.runOnUiThread {
                    if (progressDialog.isShowing) {
                        progressDialog.progress = currentProgress
                    }
                }
            }

            // 更新UI（在主线程执行）
            activity?.runOnUiThread {
                progressDialog.dismiss()
                dismiss() // 关闭对话框

                val msg = buildString {
                    append("删除完成\n")
                    append("成功: $successCount, 失败: $failedCount\n")
                    if (errors.isNotEmpty()) {
                        append("\n错误列表:")
                        errors.take(5).forEach { append("\n- $it") }
                        if (errors.size > 5) append("\n...还有 ${errors.size - 5} 个错误未显示")
                    }
                }

                // 传递删除完成状态
                moveCompleteListener?.onMoveComplete(successCount > 0)

                AlertDialog.Builder(requireContext())
                    .setTitle("删除结果")
                    .setMessage(msg)
                    .setPositiveButton("确定", null)
                    .show()
            }
        }.apply {
            start()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        copyThread?.interrupt()
        progressDialog?.dismiss()
        progressDialog = null
    }

    fun setOnMoveCompleteListener(listener: OnMoveCompleteListener) {
        this.moveCompleteListener = listener
    }
}