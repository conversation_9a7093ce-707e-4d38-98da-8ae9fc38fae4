1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.touptek.xcamview"
4    android:sharedUserId="android.uid.system"
5    android:versionCode="5002"
6    android:versionName="14" >
7
8    <uses-sdk
9        android:minSdkVersion="31"
10        android:targetSdkVersion="34" />
11
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:7:5-81
12-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:7:22-78
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:8:5-71
13-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:8:22-68
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:9:5-80
14-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:9:22-77
15    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- 适用于Android 11及以上 -->
15-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:10:5-82
15-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:10:22-79
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:11:5-66
16-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:11:22-63
17
18    <permission
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:13:5-48:19
25        android:allowBackup="true"
25-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:14:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:15:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:fullBackupContent="@xml/backup_rules"
30-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:16:9-54
31        android:icon="@mipmap/ic_launcher"
31-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:17:9-43
32        android:label="XCamView"
32-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:18:9-33
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:19:9-54
34        android:supportsRtl="true"
34-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:20:9-35
35        android:testOnly="true"
36        android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" >
36-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:21:9-68
37
38        <!-- android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" -->
39        <!-- android:theme="@style/Theme.AppCompat.DayNight" -->
40
41
42        <!-- 设置MainActivity为启动Activity -->
43        <activity
43-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:29:9-42:20
44            android:name="com.touptek.xcamview.activity.MainActivity"
44-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:30:13-70
45            android:configChanges="keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
45-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:31:13-115
46            android:exported="true"
46-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:32:13-36
47            android:launchMode="singleTask"
47-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:33:13-44
48            android:resizeableActivity="true"
48-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:34:13-46
49            android:screenOrientation="unspecified"
49-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:35:13-52
50            android:supportsPictureInPicture="true" >
50-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:36:13-52
51            <intent-filter>
51-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:37:13-41:29
52                <action android:name="android.intent.action.MAIN" />
52-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:38:17-69
52-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:38:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:39:17-77
54-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:39:27-74
55                <category android:name="android.intent.category.DEFAULT" />
55-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:40:17-76
55-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:40:27-73
56            </intent-filter>
57        </activity>
58        <activity android:name="com.touptek.xcamview.activity.videomanagement.TpVideoEncoderActivity" />
58-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:44:9-85
58-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:44:19-82
59        <activity android:name="com.touptek.xcamview.activity.videomanagement.TpVideoDecoderActivity" />
59-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:45:9-85
59-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:45:19-82
60        <activity android:name="com.touptek.xcamview.activity.browse.TpVideoBrowse" />
60-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:46:9-67
60-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:46:19-64
61
62        <!-- 图片对比功能Activity -->
63        <activity
63-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:10:9-14:66
64            android:name="com.touptek.ui.compare.TpImageCompareActivity"
64-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:11:13-73
65            android:exported="false"
65-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:12:13-37
66            android:screenOrientation="landscape"
66-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:13:13-50
67            android:theme="@style/Theme.AppCompat.NoActionBar" />
67-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:14:13-63
68        <activity
68-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:15:9-19:66
69            android:name="com.touptek.ui.compare.TpImageCompareTripleActivity"
69-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:16:13-79
70            android:exported="false"
70-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:17:13-37
71            android:screenOrientation="landscape"
71-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:18:13-50
72            android:theme="@style/Theme.AppCompat.NoActionBar" />
72-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:19:13-63
73        <activity
73-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:20:9-24:66
74            android:name="com.touptek.ui.compare.TpImageCompareMultiActivity"
74-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:21:13-78
75            android:exported="false"
75-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:22:13-37
76            android:screenOrientation="landscape"
76-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:23:13-50
77            android:theme="@style/Theme.AppCompat.NoActionBar" />
77-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\546c1888c37a525bc924540946c5c784\transformed\CodecUtils\AndroidManifest.xml:24:13-63
78
79        <provider
79-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
80            android:name="androidx.startup.InitializationProvider"
80-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
81            android:authorities="com.touptek.xcamview.androidx-startup"
81-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
82            android:exported="false" >
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
83            <meta-data
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
84                android:name="androidx.emoji2.text.EmojiCompatInitializer"
84-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
85                android:value="androidx.startup" />
85-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
86            <meta-data
86-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
87-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
88                android:value="androidx.startup" />
88-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
89            <meta-data
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
90                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
91                android:value="androidx.startup" />
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
92        </provider>
93
94        <receiver
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
95            android:name="androidx.profileinstaller.ProfileInstallReceiver"
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
96            android:directBootAware="false"
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
97            android:enabled="true"
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
98            android:exported="true"
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
99            android:permission="android.permission.DUMP" >
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
101                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
104                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
107                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
110                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
111            </intent-filter>
112        </receiver>
113    </application>
114
115</manifest>
