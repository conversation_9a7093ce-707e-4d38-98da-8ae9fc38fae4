The target system is: Android - 1 - i686
The host system is: Windows - 10.0.22631 - AMD64
Detecting C compiler ABI info compiled with the following output:
Change Dir: C:/hhx/rk3588/AndroidStudio/XCamView/app/.cxx/Debug/3t3vu28a/x86/CMakeFiles/CMakeTmp

Run Build Command(s):C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe cmTC_4263c && [1/2] Building C object CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.o

Android clang version 5.0.300080  (based on LLVM 5.0.300080)

Target: i686-none-linux-android

Thread model: posix

InstalledDir: C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin

Found candidate GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android\4.9.x

Selected GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x

Candidate multilib: .;@m32

Selected multilib: .;@m32

 "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe" -cc1 -triple i686-none-linux-android -emit-obj -mrelax-all -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mthread-model posix -mdisable-fp-elim -fmath-errno -masm-verbose -mconstructor-aliases -munwind-tables -fuse-init-array -target-cpu i686 -target-feature +ssse3 -target-linker-version 2.24 -v -dwarf-column-info -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -ffunction-sections -coverage-notes-file "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\x86\\CMakeFiles\\CMakeTmp\\CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.gcno" -resource-dir "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080" -dependency-file "CMakeFiles\\cmTC_4263c.dir\\CMakeCCompilerABI.c.o.d" -sys-header-deps -MT CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.o -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android -D __ANDROID_API__=27 -D ANDROID -isysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot -internal-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/local/include -internal-isystem "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\include" -internal-externc-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/include -internal-externc-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include -Wformat -fdebug-compilation-dir "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\x86\\CMakeFiles\\CMakeTmp" -ferror-limit 19 -fmessage-length 0 -femulated-tls -stack-protector 2 -mstackrealign -fobjc-runtime=gcc -fdiagnostics-show-option -o CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.o -x c C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c

clang -cc1 version 5.0.300080 based upon LLVM 5.0.300080 default target x86_64-unknown-linux

ignoring nonexistent directory "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/local/include"

ignoring nonexistent directory "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android

 C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\5.0.300080\include

 C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include

End of search list.

[2/2] Linking C executable cmTC_4263c

Android clang version 5.0.300080  (based on LLVM 5.0.300080)

Target: i686-none-linux-android

Thread model: posix

InstalledDir: C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin

Found candidate GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android\4.9.x

Selected GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x

Candidate multilib: .;@m32

Selected multilib: .;@m32

 "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/bin\\ld" --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86 -pie --eh-frame-hdr -m elf_i386 -dynamic-linker /system/bin/linker -o cmTC_4263c "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o" "-LC:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386" -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib --exclude-libs libgcc.a --exclude-libs libatomic.a --build-id --warn-shared-textrel --fatal-warnings --no-undefined -z noexecstack -z relro -z now --gc-sections -z nocopyreloc CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.o -lgcc -ldl -lc -lgcc -ldl "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o"




Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android]
    add: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/include]
    add: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include]
  end of search list found
  collapse include dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android]
  collapse include dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/include] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/include]
  collapse include dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include]
  implicit include dirs: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/include;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(i686-linux-android-ld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: C:/hhx/rk3588/AndroidStudio/XCamView/app/.cxx/Debug/3t3vu28a/x86/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe cmTC_4263c && [1/2] Building C object CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.o]
  ignore line: [Android clang version 5.0.300080  (based on LLVM 5.0.300080)]
  ignore line: [Target: i686-none-linux-android]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin]
  ignore line: [Found candidate GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android\4.9.x]
  ignore line: [Selected GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m32]
  ignore line: [Selected multilib: .]
  ignore line: [@m32]
  ignore line: [ "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe" -cc1 -triple i686-none-linux-android -emit-obj -mrelax-all -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mthread-model posix -mdisable-fp-elim -fmath-errno -masm-verbose -mconstructor-aliases -munwind-tables -fuse-init-array -target-cpu i686 -target-feature +ssse3 -target-linker-version 2.24 -v -dwarf-column-info -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -ffunction-sections -coverage-notes-file "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\x86\\CMakeFiles\\CMakeTmp\\CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.gcno" -resource-dir "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080" -dependency-file "CMakeFiles\\cmTC_4263c.dir\\CMakeCCompilerABI.c.o.d" -sys-header-deps -MT CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.o -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android -D __ANDROID_API__=27 -D ANDROID -isysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot -internal-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/local/include -internal-isystem "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\include" -internal-externc-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/include -internal-externc-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include -Wformat -fdebug-compilation-dir "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\x86\\CMakeFiles\\CMakeTmp" -ferror-limit 19 -fmessage-length 0 -femulated-tls -stack-protector 2 -mstackrealign -fobjc-runtime=gcc -fdiagnostics-show-option -o CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.o -x c C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c]
  ignore line: [clang -cc1 version 5.0.300080 based upon LLVM 5.0.300080 default target x86_64-unknown-linux]
  ignore line: [ignoring nonexistent directory "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/local/include"]
  ignore line: [ignoring nonexistent directory "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android]
  ignore line: [ C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\5.0.300080\include]
  ignore line: [ C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking C executable cmTC_4263c]
  ignore line: [Android clang version 5.0.300080  (based on LLVM 5.0.300080)]
  ignore line: [Target: i686-none-linux-android]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin]
  ignore line: [Found candidate GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android\4.9.x]
  ignore line: [Selected GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m32]
  ignore line: [Selected multilib: .]
  ignore line: [@m32]
  link line: [ "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/bin\\ld" --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86 -pie --eh-frame-hdr -m elf_i386 -dynamic-linker /system/bin/linker -o cmTC_4263c "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o" "-LC:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386" -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib --exclude-libs libgcc.a --exclude-libs libatomic.a --build-id --warn-shared-textrel --fatal-warnings --no-undefined -z noexecstack -z relro -z now --gc-sections -z nocopyreloc CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.o -lgcc -ldl -lc -lgcc -ldl "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o"]
    arg [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/bin\\ld] ==> ignore
    arg [--sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86] ==> ignore
    arg [-pie] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_i386] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_4263c] ==> ignore
    arg [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o] ==> obj [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o]
    arg [-LC:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386] ==> dir [C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386]
    arg [-LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x] ==> dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x]
    arg [-LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib] ==> dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib]
    arg [-LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib] ==> dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib]
    arg [--exclude-libs] ==> ignore
    arg [libgcc.a] ==> ignore
    arg [--exclude-libs] ==> ignore
    arg [libatomic.a] ==> ignore
    arg [--build-id] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-znow] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [-znocopyreloc] ==> ignore
    arg [CMakeFiles/cmTC_4263c.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-lgcc] ==> lib [gcc]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [-lgcc] ==> lib [gcc]
    arg [-ldl] ==> lib [dl]
    arg [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o] ==> obj [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o]
  collapse obj [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib/crtbegin_dynamic.o]
  collapse obj [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib/crtend_android.o]
  collapse library dir [C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/lib/linux/i386]
  collapse library dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x]
  collapse library dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/i686-linux-android/lib]
  collapse library dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib]
  implicit libs: [gcc;dl;c;gcc;dl]
  implicit objs: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib/crtbegin_dynamic.o;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib/crtend_android.o]
  implicit dirs: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/lib/linux/i386;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/i686-linux-android/lib;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: C:/hhx/rk3588/AndroidStudio/XCamView/app/.cxx/Debug/3t3vu28a/x86/CMakeFiles/CMakeTmp

Run Build Command(s):C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe cmTC_46f87 && [1/2] Building CXX object CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.o

Android clang version 5.0.300080  (based on LLVM 5.0.300080)

Target: i686-none-linux-android

Thread model: posix

InstalledDir: C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin

Found candidate GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android\4.9.x

Selected GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x

Candidate multilib: .;@m32

Selected multilib: .;@m32

 "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe" -cc1 -triple i686-none-linux-android -emit-obj -mrelax-all -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mthread-model posix -mdisable-fp-elim -fmath-errno -masm-verbose -mconstructor-aliases -munwind-tables -fuse-init-array -target-cpu i686 -target-feature +ssse3 -target-linker-version 2.24 -v -dwarf-column-info -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -ffunction-sections -coverage-notes-file "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\x86\\CMakeFiles\\CMakeTmp\\CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.gcno" -resource-dir "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080" -dependency-file "CMakeFiles\\cmTC_46f87.dir\\CMakeCXXCompilerABI.cpp.o.d" -sys-header-deps -MT CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.o -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/libs/x86/include -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include/backward -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android -D __ANDROID_API__=27 -D ANDROID -isysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot -internal-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/local/include -internal-isystem "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\include" -internal-externc-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/include -internal-externc-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include -Wformat -std=c++11 -fdeprecated-macro -fdebug-compilation-dir "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\x86\\CMakeFiles\\CMakeTmp" -ferror-limit 19 -fmessage-length 0 -femulated-tls -stack-protector 2 -mstackrealign -fobjc-runtime=gcc -fcxx-exceptions -fexceptions -fdiagnostics-show-option -o CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.o -x c++ C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp

clang -cc1 version 5.0.300080 based upon LLVM 5.0.300080 default target x86_64-unknown-linux

ignoring nonexistent directory "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/local/include"

ignoring nonexistent directory "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include

 C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/libs/x86/include

 C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include/backward

 C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android

 C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\5.0.300080\include

 C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include

End of search list.

[2/2] Linking CXX executable cmTC_46f87

Android clang version 5.0.300080  (based on LLVM 5.0.300080)

Target: i686-none-linux-android

Thread model: posix

InstalledDir: C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin

Found candidate GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android\4.9.x

Selected GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x

Candidate multilib: .;@m32

Selected multilib: .;@m32

 "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/bin\\ld" --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86 -pie --eh-frame-hdr -m elf_i386 -dynamic-linker /system/bin/linker -o cmTC_46f87 "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o" "-LC:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386" -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib --exclude-libs libgcc.a --exclude-libs libatomic.a --build-id --warn-shared-textrel --fatal-warnings --no-undefined -z noexecstack -z relro -z now --gc-sections -z nocopyreloc CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc -lgcc -ldl -lc -lgcc -lgcc -ldl "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o"




Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include]
    add: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/libs/x86/include]
    add: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include/backward]
    add: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android]
    add: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/include]
    add: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include]
  end of search list found
  collapse include dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include]
  collapse include dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/libs/x86/include] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/libs/x86/include]
  collapse include dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include/backward] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include/backward]
  collapse include dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android]
  collapse include dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/include] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/include]
  collapse include dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include]
  implicit include dirs: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/libs/x86/include;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include/backward;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/include;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(i686-linux-android-ld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: C:/hhx/rk3588/AndroidStudio/XCamView/app/.cxx/Debug/3t3vu28a/x86/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe cmTC_46f87 && [1/2] Building CXX object CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [Android clang version 5.0.300080  (based on LLVM 5.0.300080)]
  ignore line: [Target: i686-none-linux-android]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin]
  ignore line: [Found candidate GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android\4.9.x]
  ignore line: [Selected GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m32]
  ignore line: [Selected multilib: .]
  ignore line: [@m32]
  ignore line: [ "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe" -cc1 -triple i686-none-linux-android -emit-obj -mrelax-all -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mthread-model posix -mdisable-fp-elim -fmath-errno -masm-verbose -mconstructor-aliases -munwind-tables -fuse-init-array -target-cpu i686 -target-feature +ssse3 -target-linker-version 2.24 -v -dwarf-column-info -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -ffunction-sections -coverage-notes-file "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\x86\\CMakeFiles\\CMakeTmp\\CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.gcno" -resource-dir "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080" -dependency-file "CMakeFiles\\cmTC_46f87.dir\\CMakeCXXCompilerABI.cpp.o.d" -sys-header-deps -MT CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.o -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/libs/x86/include -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include/backward -isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android -D __ANDROID_API__=27 -D ANDROID -isysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot -internal-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/local/include -internal-isystem "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\include" -internal-externc-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/include -internal-externc-isystem C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include -Wformat -std=c++11 -fdeprecated-macro -fdebug-compilation-dir "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\x86\\CMakeFiles\\CMakeTmp" -ferror-limit 19 -fmessage-length 0 -femulated-tls -stack-protector 2 -mstackrealign -fobjc-runtime=gcc -fcxx-exceptions -fexceptions -fdiagnostics-show-option -o CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.o -x c++ C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [clang -cc1 version 5.0.300080 based upon LLVM 5.0.300080 default target x86_64-unknown-linux]
  ignore line: [ignoring nonexistent directory "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/local/include"]
  ignore line: [ignoring nonexistent directory "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include]
  ignore line: [ C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/libs/x86/include]
  ignore line: [ C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sources/cxx-stl/gnu-libstdc++/4.9/include/backward]
  ignore line: [ C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include/i686-linux-android]
  ignore line: [ C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\5.0.300080\include]
  ignore line: [ C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking CXX executable cmTC_46f87]
  ignore line: [Android clang version 5.0.300080  (based on LLVM 5.0.300080)]
  ignore line: [Target: i686-none-linux-android]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin]
  ignore line: [Found candidate GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android\4.9.x]
  ignore line: [Selected GCC installation: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m32]
  ignore line: [Selected multilib: .]
  ignore line: [@m32]
  link line: [ "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/bin\\ld" --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86 -pie --eh-frame-hdr -m elf_i386 -dynamic-linker /system/bin/linker -o cmTC_46f87 "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o" "-LC:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386" -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib -LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib --exclude-libs libgcc.a --exclude-libs libatomic.a --build-id --warn-shared-textrel --fatal-warnings --no-undefined -z noexecstack -z relro -z now --gc-sections -z nocopyreloc CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc -lgcc -ldl -lc -lgcc -lgcc -ldl "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o"]
    arg [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/bin\\ld] ==> ignore
    arg [--sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86] ==> ignore
    arg [-pie] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_i386] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_46f87] ==> ignore
    arg [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o] ==> obj [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o]
    arg [-LC:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386] ==> dir [C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386]
    arg [-LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x] ==> dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x]
    arg [-LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib] ==> dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib]
    arg [-LC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib] ==> dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib]
    arg [--exclude-libs] ==> ignore
    arg [libgcc.a] ==> ignore
    arg [--exclude-libs] ==> ignore
    arg [libatomic.a] ==> ignore
    arg [--build-id] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-znow] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [-znocopyreloc] ==> ignore
    arg [CMakeFiles/cmTC_46f87.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc] ==> lib [gcc]
    arg [-lgcc] ==> lib [gcc]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [-lgcc] ==> lib [gcc]
    arg [-lgcc] ==> lib [gcc]
    arg [-ldl] ==> lib [dl]
    arg [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o] ==> obj [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o]
  collapse obj [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtbegin_dynamic.o] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib/crtbegin_dynamic.o]
  collapse obj [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib\\crtend_android.o] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib/crtend_android.o]
  collapse library dir [C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\5.0.300080\\lib\\linux\\i386] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/lib/linux/i386]
  collapse library dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x]
  collapse library dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x/../../../../i686-linux-android/lib] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/i686-linux-android/lib]
  collapse library dir [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib] ==> [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib]
  implicit libs: [stdc++;m;gcc;gcc;dl;c;gcc;gcc;dl]
  implicit objs: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib/crtbegin_dynamic.o;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib/crtend_android.o]
  implicit dirs: [C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/5.0.300080/lib/linux/i386;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/lib/gcc/i686-linux-android/4.9.x;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/x86-4.9/prebuilt/windows-x86_64/i686-linux-android/lib;C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/platforms/android-27/arch-x86/usr/lib]
  implicit fwks: []


